# %% ----------------------------------------------------
# Block 1: Imports
from datetime import datetime, timedelta
import io
import gzip
import time
import pandas as pd
import json
import requests
import pprint
pp = pprint.PrettyPrinter(indent=4)


print("hi")

# %% ----------------------------------------------------
# Block 2: Grundkonfiguration
client_id = ""
client_secret = ""
refresh_token = ''
countryCode = 'ES'
report_name = "SP campaigns report 2025/02"
startDate = "2025-06-01"
endDate = "2025-06-30"


# %% ----------------------------------------------------
# Block 3: Access Token mit dem Refresh Token herausfinden
url_token = "https://api.amazon.co.uk/auth/o2/token"
payload_token = {
    'grant_type': "refresh_token",
    'client_id': client_id,
    'refresh_token': refresh_token,
    'client_secret': client_secret
}
headers_token = {
    'Content-Type': 'application/x-www-form-urlencoded'
}
response_token = requests.request(
    "POST", url_token, headers=headers_token, data=payload_token)

if response_token.status_code == 200:  # Checken ob Request erfolgreich war
    # JSON response_token in ein Python dictionary parsen
    data_token = response_token.json()
    # Jetzt kann ich mit dem data_token als ein Python dictionary arbeiten
    print(data_token)
    access_token = data_token["access_token"]
else:
    print(f"Request failed with status code {response_token.status_code}")


# %% ----------------------------------------------------
# Block 4: Profile/Accounts/Marktplätze abrufen und passende profileId/Marktplatz ID wählen:

url_profiles = "https://advertising-api-eu.amazon.com/v2/profiles"
payload_profiles = {}
headers_profiles = {
    'Amazon-Advertising-API-ClientId': client_id,
    'Authorization': 'Bearer ' + access_token
}
response_profiles = requests.request(
    "GET", url_profiles, headers=headers_profiles, data=payload_profiles)

if response_profiles.status_code == 200:  # Checken ob Abfrage erfolgreich war
    # JSON response_profiles in ein Python dictionary parsen
    data_profiles = response_profiles.json()
    # Jetzt kann ich mit dem data_profiles als ein Python dictionary arbeiten
    pp.pprint(data_profiles)
else:
    print(f"Request failed with status code {response_profiles.status_code}")

profileId = ''
for account in data_profiles:  # Durchläuft alle Profile
    if account['accountInfo']['name'] == 'Travel Dude Store' and account['countryCode'] == countryCode:
        profileId = str(account['profileId'])
        print('Found Profile ID: ' + profileId)
        break
if not profileId:
    raise Exception(
        'No suitable Profile with given countryCode ' + countryCode + ' found.')


# %% ----------------------------------------------------
# Block 5: Authentifizierungsinformationen
ACCESS_TOKEN = access_token
API_URL = 'https://advertising-api-eu.amazon.com'
HEADERS = {
    'Authorization': f'Bearer {ACCESS_TOKEN}',
    'Content-Type': 'application/json',
    'Amazon-Advertising-API-ClientId': client_id,
    'Amazon-Advertising-API-Scope': profileId,
    'Accept': 'application/json'
}


# %% ----------------------------------------------------
# Block 6: Settings Klasse
class Settings:
    def __init__(self):
        self.TEST_MODE = True  # nur begrenzte Anzahl verarbeiten
        self.TEST_LIMIT = 600   # Limit der Anzahl an Keywords im Test
        self.DAYS_BACK = 30    # Zeitraum für Metriken
        self.API_DELAY = 0.2   # Pause zwischen API-Calls (in Sekunden)


settings = Settings()  # Initialisiert Settings


# %% ----------------------------------------------------
# Block 7: Alle aktiven Kampagnen-IDs von einem Marktplatz abrufen
def get_all_campaign_ids():
    campaign_ids = []
    start_index = 0
    count = 50  # Anzahl der Kampagnen pro API-Request
    while True:
        try:
            response = requests.get(
                f'{API_URL}/v2/campaigns',
                headers=HEADERS,
                params={'startIndex': start_index,
                        'count': count, 'stateFilter': 'enabled'}
            )

            if response.status_code != 200:
                pp.print(
                    f"Fehler beim Abrufen der Kampagnen: {response.json()}")
                break
            campaigns = response.json()
            if not campaigns:
                break  # Keine weiteren Kampagnen verfügbar

            for campaign in campaigns:
                campaign_ids.append(campaign['campaignId'])
            start_index += count  # Nächste Seite abrufen
        except Exception as e:
            pp.print(f"Fehler beim Abrufen der Kampagnen: {e}")
            break

    print(f"{len(campaign_ids)} Kampagnen-IDs abgerufen")
    return campaign_ids


# %% ----------------------------------------------------
# Block 8: Zu jeder Kampagne werden die Keywords eingesammelt und in einer Liste gespeichert
def get_all_keywords_from_campaigns(campaign_ids, settings):
    all_keywords = []
    keyword_count = 0

    for campaign_id in campaign_ids:
        if settings.TEST_MODE and keyword_count >= settings.TEST_LIMIT:
            print(
                f"Test-Modus: Limit von {settings.TEST_LIMIT} Keywords erreicht")
            break
        start_index = 0
        count = 50  # Anzahl der Keywords pro API-Request
        while True:
            try:
                response = requests.get(
                    f'{API_URL}/v2/keywords',
                    headers=HEADERS,
                    params={'campaignIdFilter': campaign_id,
                            'startIndex': start_index, 'count': count}
                )
                if response.status_code != 200:
                    print(
                        f"Fehler beim Abrufen der Keywords für Kampagne {campaign_id}: {response.json()}")
                    break
                keywords = response.json()
                if not keywords:
                    break  # Keine weiteren Keywords verfügbar
                for keyword in keywords:
                    all_keywords.append({
                        'campaignId': campaign_id,
                        'keywordId': keyword['keywordId'],
                        'keywordText': keyword['keywordText'],
                        'matchType': keyword['matchType'],
                        'bid': keyword['bid'],
                        'state': keyword['state']
                    })
                    keyword_count += 1
                    if settings.TEST_MODE and keyword_count >= settings.TEST_LIMIT:
                        break
                if settings.TEST_MODE and keyword_count >= settings.TEST_LIMIT:
                    break
                start_index += count  # Nächste Seite abrufen
            except Exception as e:
                print(
                    f"Fehler beim Abrufen der Keywords für Kampagne {campaign_id}: {e}")
                break
        # Wartezeit, um API-Rate-Limits zu vermeiden
        time.sleep(settings.API_DELAY)
    print(f"Insgesamt {len(all_keywords)} Keywords abgerufen")
    return all_keywords


# %% ----------------------------------------------------
# Block 9: Gebotsempfehlungen pro Keyword abrufen
def get_bid_recommendations(keywords, settings):
    if not keywords:
        print("Keine Keywords zum Verarbeiten vorhanden.")
        return []
    try:
        for keyword in keywords:
            try:
                response = requests.get(
                    f'{API_URL}/v2/sp/keywords/{keyword["keywordId"]}/bidRecommendations',
                    headers=HEADERS
                )
                if response.status_code == 200:
                    recommendation = response.json()
                    keyword['suggestedBid'] = recommendation.get(
                        'suggestedBid', None)
                    keyword['minBid'] = recommendation.get('minimumBid', None)
                    keyword['maxBid'] = recommendation.get('maximumBid', None)
                else:
                    print(
                        f"Fehler beim Abrufen der Bid-Empfehlung für Keyword {keyword['keywordId']}: {response.json()}")
            except Exception as e:
                print(
                    f"Fehler beim Abrufen der Bid-Empfehlung für Keyword {keyword['keywordId']}: {e}")
            time.sleep(settings.API_DELAY)
        return keywords
    except Exception as e:
        print(f"Fehler in get_bid_recommendations: {e}")
        return keywords


# %% ----------------------------------------------------
# Block 10: KPIs pro Keyword abrufen (ACoS, Clicks, Sales)
def get_performance_metrics(keywords, settings):
    if not keywords:
        print("Keine Keywords zum Verarbeiten vorhanden.")
        return []
    try:
        for keyword in keywords:
            try:
                end_date = datetime.now()
                start_date = end_date - timedelta(days=settings.DAYS_BACK)
                report_payload = {  # Erstellt einen Report request
                    "reportDate": end_date.strftime('%Y-%m-%d'),
                    "metrics": ["impressions", "clicks", "cost", "sales", "acos"],
                    "timeUnit": "SUMMARY",
                    "keywordId": keyword["keywordId"]
                }
                response = requests.post(  # Request report
                    f'{API_URL}/v2/sp/keywords/report',
                    headers=HEADERS,
                    json=report_payload
                )
                if response.status_code == 200:
                    report_data = response.json()
                    print("-----")
                    print(report_data)
                    print("-----")
                    # Extrahiert Metriken aus Report
                    if 'rows' in report_data and len(report_data['rows']) > 0:
                        metrics = report_data['rows'][0]
                        keyword.update({
                            'impressions': metrics.get('impressions', 0),
                            'clicks': metrics.get('clicks', 0),
                            'spend': metrics.get('cost', 0),
                            'sales': metrics.get('sales', 0),
                            'acos': metrics.get('acos', 0)
                        })
                    else:
                        # No data available, initialize with zeros
                        keyword.update({
                            'impressions': 0,
                            'clicks': 0,
                            'spend': 0,
                            'sales': 0,
                            'acos': 0
                        })
                else:
                    response = requests.get(
                        f'{API_URL}/v2/sp/keywords/{keyword["keywordId"]}/stats',
                        headers=HEADERS,
                        params={
                            'startDate': start_date.strftime('%Y-%m-%d'),
                            'endDate': end_date.strftime('%Y-%m-%d')
                        }
                    )
                    if response.status_code == 200:
                        metrics = response.json()
                        keyword.update({
                            'impressions': metrics.get('impressions', 0),
                            'clicks': metrics.get('clicks', 0),
                            'spend': metrics.get('cost', 0),
                            'sales': metrics.get('sales', 0),
                            'acos': metrics.get('acos', 0)
                        })
                    else:
                        print(
                            f"Fehler beim Abrufen der Metriken für Keyword {keyword['keywordId']}: {response.json()}")
                        print(f"Status Code: {response.status_code}")
                        print(f"URL verwendet: {response.url}")
                        # Initialize metrics with zero values
                        keyword.update({
                            'impressions': 0,
                            'clicks': 0,
                            'spend': 0,
                            'sales': 0,
                            'acos': 0
                        })
            except Exception as e:
                print(
                    f"Fehler beim Abrufen der Metriken für Keyword {keyword['keywordId']}: {e}")
                keyword.update({
                    'impressions': 0,
                    'clicks': 0,
                    'spend': 0,
                    'sales': 0,
                    'acos': 0
                })
            time.sleep(settings.API_DELAY)
        return keywords
    except Exception as e:
        print(f"Fehler in get_performance_metrics: {e}")
        return keywords


# %% ----------------------------------------------------
# Block 11: ACoS je Keyword abrufen
def get_acos_for_keywords(keywords):
    if not keywords:  # Checkt ob Keywords None oder Leer ist
        print("Keine Keywords zum Verarbeiten vorhanden.")
        return []
    try:
        for keyword in keywords:
            try:
                response = requests.get(
                    f'{API_URL}/sp/keywords/{keyword["keywordId"]}/metrics',
                    f'{API_URL}/sp/keywords/{keyword["keywordId"]}/metrics',
                    headers=HEADERS
                )
                if response.status_code == 200:
                    metrics = response.json()
                    keyword['acos'] = metrics.get('acos', None)
                else:
                    print(
                        f"Fehler beim Abrufen der ACoS-Daten für Keyword {keyword['keywordId']}: {response.json()}")
            except Exception as e:
                print(
                    f"Fehler beim Abrufen der ACoS-Daten für Keyword {keyword['keywordId']}: {e}")
            time.sleep(0.5)  # Vermeidung von API-Limits
        return keywords  # Gibt modifizierte Keyword Liste zurück
    except Exception as e:
        print(f"Fehler in get_acos_for_keywords: {e}")
        return keywords  # Return the original keywords list in case of error


# %% ----------------------------------------------------
# Block 12: Packt Keyword-Liste in Data Frame
def create_dataframe(keywords):
    """  
    Alternative version using json_normalize to handle nested structures.  
    """
    # Create DataFrame using json_normalize
    df = pd.json_normalize(keywords)

    # Rename columns to more readable format if they exist
    if 'suggestedBid.suggested' in df.columns:
        df = df.rename(columns={
            'suggestedBid.suggested': 'suggested_bid',
            'suggestedBid.rangeStart': 'suggested_bid_range_start',
            'suggestedBid.rangeEnd': 'suggested_bid_range_end'
        })

    return df


# %% ----------------------------------------------------
# Block 13: Hauptprogramm, welches CampaignIDs abruft und alle Keywords rausholt
print("Starte den Abruf aller CampaignIds...")
print(f"{'TEST-MODUS' if settings.TEST_MODE else 'PRODUCTION-MODUS'}")
campaign_ids = get_all_campaign_ids()
if campaign_ids:
    print("Starte den Abruf aller Keywords aus den Kampagnen...")
    all_keywords = get_all_keywords_from_campaigns(campaign_ids, settings)
    if all_keywords:
        print("Starte den Abruf der Bid-Empfehlungen...")
        all_keywords = get_bid_recommendations(all_keywords, settings)
        if all_keywords:
            print("Starte den Abruf der Performance-Metriken...")
            all_keywords = get_performance_metrics(all_keywords, settings)
            if all_keywords:
                print(
                    f"Insgesamt {len(all_keywords)} Keywords mit allen Metriken abgerufen.")
                # Erstellt den Pandas DataFrame mit den Keywords
                df = create_dataframe(all_keywords)
                # Zeigt die ersten Zeilen des Data Frames an
                print(df.head(10))
            else:
                print("❌ Fehler beim Abrufen der Performance-Metriken!")
        else:
            print("❌ Fehler beim Abrufen der Bid-Empfehlungen!")
    else:
        print("❌ Keine Keywords in den Kampagnen gefunden!")
else:
    print("❌ Keine Kampagnen gefunden!")


# %% ----------------------------------------------------
# Block 14: Zeigt (in Jupyter) die tabellarische Vorschau
df


# %% ----------------------------------------------------
# Block 15: Legt Reporting-Job an
url = "https://advertising-api-eu.amazon.com/reporting/reports"
payload = {
    "name": report_name,
    # "date": "2025-02-10",
    "startDate": startDate,
    "endDate": endDate,
    "configuration": {
        "adProduct": "SPONSORED_PRODUCTS",
        "groupBy": ["adGroup"],
        "columns": [
            "impressions",
            "clicks",
            "cost",
            "attributedConversions1d",
            "attributedConversions7d",
            "attributedConversions14d",
            "attributedConversions30d",
            "purchases1d",
            "purchases7d",
            "purchases14d",
            "purchases30d",
            "attributedConversions1dSameSKU",
            "attributedConversions7dSameSKU",
            "attributedConversions14dSameSKU",
            "attributedConversions30dSameSKU",
            "purchasesSameSku1d",
            "purchasesSameSku7d",
            "purchasesSameSku14d",
            "purchasesSameSku30d",
            "attributedUnitsOrdered1d",
            "attributedUnitsOrdered7d",
            "attributedUnitsOrdered14d",
            "attributedUnitsOrdered30d",
            "unitsSoldClicks1d",
            "unitsSoldClicks7d",
            "unitsSoldClicks14d",
            "unitsSoldClicks30d",
            "attributedSales1d",
            "attributedSales7d",
            "attributedSales14d",
            "attributedSales30d",
            "sales1d",
            "sales7d",
            "sales14d",
            "sales30d",
            "attributedSales1dSameSKU",
            "attributedSales7dSameSKU",
            "attributedSales14dSameSKU",
            "attributedSales30dSameSKU",
            "attributedSalesSameSku1d",
            "attributedSalesSameSku7d",
            "attributedSalesSameSku14d",
            "attributedSalesSameSku30d",
            "attributedUnitsOrdered1dSameSKU",
            "attributedUnitsOrdered7dSameSKU",
            "attributedUnitsOrdered14dSameSKU",
            "attributedUnitsOrdered30dSameSKU",
            "unitsSoldSameSku1d",
            "unitsSoldSameSku7d",
            "unitsSoldSameSku14d",
            "unitsSoldSameSku30d",
            "attributedKindleEditionNormalizedPagesRead14d",
            "attributedKindleEditionNormalizedPagesRoyalties14d",
            "kindleEditionNormalizedPagesRead14d",
            "kindleEditionNormalizedPagesRoyalties14d",
            "keywordId",
            "keywordText",
            "keyword",
            "matchType",
            "currency",
            "campaignBudgetCurrencyCode",
            # "date",
            "startDate",
            "endDate",
            "topOfSearchImpressionShare"
        ],
        "reportTypeId": "spKeywords",
        # "reportTypeId": "spAdvertisedProduct",
        "timeUnit": "SUMMARY",
        "format": "GZIP_JSON"
    }
}
headers = {
    'Amazon-Advertising-API-ClientId': client_id,
    'Amazon-Advertising-API-Scope': str(profileId),
    'Authorization': 'Bearer ' + access_token,
    'Content-Type': 'application/json'
}
response = requests.post(url, headers=headers, data=json.dumps(payload))
print(response.text)
try:
    report_id = response.json().get('reportId')
    print(f"Report ID: {report_id}")
except json.JSONDecodeError:
    print("Failed to parse response as JSON")
except KeyError:
    print("Report ID not found in response")


# %% ----------------------------------------------------
# Block 16: Report Status und Download-URL holen
report_url = ""
counter = 0
while counter < 30:
    url = "https://advertising-api-eu.amazon.com/reporting/reports/" + report_id
    payload = {}
    headers = {
        'Amazon-Advertising-API-ClientId': client_id,
        'Amazon-Advertising-API-Scope': profileId,
        'Authorization': 'Bearer ' + access_token,
        'Content-Type': 'application/json'
    }
    response = requests.request("GET", url, headers=headers, data=payload)
    print(response.text)
    print(response.json().get('url'))
    if response.status_code == 200:  # Checkt ob Response Status erfolgreich
        # Extrahiert die 'url' aus der JSON response
        report_url = response.json().get('url')
        print(report_url)
        if report_url:
            break
        counter = counter + 1
        time.sleep(5)
    else:
        print(f"Failed to retrieve report: {response.status_code}")


# %% ----------------------------------------------------
# Block 17: Lädt gzipped JSON file von URL, extrahiert Reports und merged mit existierendem df
def download_and_process_reports(url, existing_df):
    # Schritt 1: Gzipped file runterladen
    print("Downloading file from URL...")
    response = requests.get(url, stream=True)
    if response.status_code != 200:
        raise Exception(
            f"Failed to download file: HTTP Status {response.status_code}")
    print("Decompressing gzip file...")  # Schritt 2: Gzip file dekomprimieren
    with gzip.GzipFile(fileobj=io.BytesIO(response.content)) as gzipped_file:
        json_content = gzipped_file.read().decode('utf-8')
    print("Parsing JSON content...")  # Schritt 3: Den JSON Inhalt parsen
    # Checkt ob es ein JSON Array oder ein single JSON object ist
    if json_content.strip().startswith('['):
        reports = json.loads(json_content)
    else:  # Falls jede Zeile ein separates JSON object ist
        reports = []
        for line in json_content.splitlines():
            if line.strip():
                reports.append(json.loads(line))
    print(f"Loaded {len(reports)} reports")
    # Schritt 4: Extrahiere Keyword Information aus reports
    print("Processing reports...")
    keywords_data = []
    for report in reports:
        # Checkt ob es ein Keyword report ist (sollte eine keywordId haben)
        if 'keywordId' not in report:
            continue
        keyword_info = {
            'keywordId': report.get('keywordId'),
            'keywordText': report.get('keywordText'),
            'matchType': report.get('matchType'),
            'impressions': report.get('impressions', 0),
            'clicks': report.get('clicks', 0),
            # kann auch 'spend' in unserer df heißen
            'cost': report.get('cost', 0),
            'startDate': report.get('startDate'),
            'endDate': report.get('endDate')
        }
        for metric in ['sales1d', 'sales7d', 'sales14d', 'sales30d',  # Verkaufsmetriken hinzufügen
                       'attributedSales1d', 'attributedSales7d',
                       'attributedSales14d', 'attributedSales30d']:
            if metric in report:
                keyword_info[metric] = report[metric]

        keywords_data.append(keyword_info)
    if not keywords_data:  # Schritt 5: DataFrame aus den extrahierten Daten erstellen
        print("No keyword reports found in the data")
        return existing_df
    reports_df = pd.DataFrame(keywords_data)
    print(f"Created DataFrame with {len(reports_df)} keyword reports")
    # Schritt 6: Mit existierendem DataFrame (keywordId) vereinen, und keywordID zum selben Typen in beiden DataFrames konvertieren (zum leichteren Vereinen)
    existing_df['keywordId'] = existing_df['keywordId'].astype(str)
    reports_df['keywordId'] = reports_df['keywordId'].astype(str)
    print("Merging with existing DataFrame...")
    merged_df = existing_df.merge(
        reports_df,
        on='keywordId',
        how='left',
        suffixes=('', '_report')
    )
    print(
        f"Final DataFrame has {len(merged_df)} rows and {len(merged_df.columns)} columns")
    return merged_df


pd.set_option('display.max_columns', None)  # Alle Spalten anzeigen
pd.set_option('display.width', None)
result_df = download_and_process_reports(report_url, df)
result_df.columns


# %% ----------------------------------------------------
# Block 18: ACoS in verschiedenen Zeitfenstern kalkulieren und zum DataFrame hinzufügen
def calculate_acos(df):
    result_df = df.copy()  # Erst Kopie erstellen
    for period in ['1d', '7d', '14d', '30d']:  # ACoS für Verkäufe berechnen
        acos_column = f'acos_{period}'  # neuen Spaltennamen erstellen
        sales_column = f'sales{period}'
        if sales_column in df.columns:  # Berechnung des ACoS mit BEachtung von Division durch 0
            cost = df['cost'].fillna(0)  # NaN durch 0 ersetzen
            sales = df[sales_column].fillna(0)
            # Wenn Verkäufe 0 sind, so wird ACoS auf NaN gesetzt
            result_df[acos_column] = (
                cost / sales.replace(0, float('nan'))) * 100

    # Calculate ACoS for attributed sales
    for period in ['1d', '7d', '14d', '30d']:
        # Create column name
        acos_column = f'attributed_acos_{period}'
        sales_column = f'attributedSales{period}'

        # Calculate ACoS (handle division by zero)
        if sales_column in df.columns:
            # Replace NaN with 0 for calculations
            cost = df['cost'].fillna(0)
            sales = df[sales_column].fillna(0)

            # Calculate ACoS as a percentage (cost/sales * 100)
            # When sales is 0, set ACoS to NaN
            result_df[acos_column] = (cost / sales.replace(0, float('nan')))

    # Format ACoS columns for better readability (optional)
    for col in result_df.columns:
        if col.startswith('acos_') or col.startswith('attributed_acos_'):
            # Round to 2 decimal places
            result_df[col] = result_df[col].round(2)

    return result_df


# Example usage:
df_with_acos = calculate_acos(result_df)
df_with_acos


# %%
# 1 iteration of Bid Adjustments (nur ACOS wird betrachtet)
def optimize_bids_iteration1(keywords_df):
    """
    Erste Iteration: Grundlegende ACOS-basierte Optimierung mit 7-Tage-Daten
    """
    results = []

    for index, keyword in keywords_df.iterrows():
        current_bid = keyword['bid']
        keyword_id = keyword['keywordId']
        keyword_text = keyword['keywordText']
        state = keyword['state']
        suggested_bid = keyword.get('suggested_bid', 0)
        acos_7d = keyword.get('attributed_acos_7d')

        # Nur Keywords mit ACOS-Werten und im Status "enabled" berücksichtigen
        if pd.isna(acos_7d) or state != 'enabled':
            results.append({
                'keywordId': keyword_id,
                'keywordText': keyword_text,
                'current_bid': current_bid,
                'new_bid': current_bid,  # Keine Änderung
                'adjustment_factor': 1.0,
                'acos': acos_7d,
                'state': state,
                'action': "Keine Änderung - Kein ACOS-Wert oder nicht aktiviert"
            })
            continue

        # ACOS in Prozent umwandeln für bessere Lesbarkeit
        acos_percent = acos_7d * 100

        # Anpassung basierend auf der PPC-Guideline
        if acos_percent < 5:  # 1-5%
            adjustment_factor = 2.0  # 100% erhöhen
            reason = "Sehr niedriger ACOS (1-5%)"
            action = "Gebot erhöhen"
        elif acos_percent < 10:  # 5-10%
            adjustment_factor = 1.5  # 50% erhöhen
            reason = "Niedriger ACOS (5-10%)"
            action = "Gebot erhöhen"
        elif acos_percent < 15:  # 10-15%
            adjustment_factor = 1.3  # 30% erhöhen
            reason = "Guter ACOS (10-15%)"
            action = "Gebot erhöhen"
        elif acos_percent < 20:  # 15-20%
            adjustment_factor = 1.1  # 10% erhöhen
            reason = "Akzeptabler ACOS (15-20%)"
            action = "Gebot leicht erhöhen"
        elif acos_percent < 30:  # 20-30%
            adjustment_factor = 0.85  # 15% reduzieren
            reason = "Erhöhter ACOS (20-30%)"
            action = "Gebot senken"
        elif acos_percent < 80:  # 30-80%
            adjustment_factor = 0.6  # 40% reduzieren
            reason = "Hoher ACOS (30-80%)"
            action = "Gebot stark senken"
        else:  # > 80%
            adjustment_factor = 0.28 / current_bid  # Auf 0.28€ setzen
            reason = "Sehr hoher ACOS (>80%)"
            action = "Gebot auf 0.28€ setzen (Vorstufe zur Deaktivierung)"

        # Neues Gebot berechnen
        new_bid = round(current_bid * adjustment_factor, 2)

        # Sicherstellen, dass das neue Gebot nicht unter 0.28€ fällt
        if new_bid < 0.28 and acos_percent < 80:
            new_bid = 0.28
            action = "Gebot auf Minimum von 0.28€ gesetzt"

        results.append({
            'keywordId': keyword_id,
            'keywordText': keyword_text,
            'current_bid': current_bid,
            'new_bid': new_bid,
            'adjustment_factor': adjustment_factor,
            'acos': acos_7d,
            'state': state,
            'reason': reason,
            'action': action
        })

    return pd.DataFrame(results)


optimize_bids_iteration1_df = optimize_bids_iteration1(df_with_acos)
optimize_bids_iteration1_df

# %%
# outdated


def update_keyword_bids(adjusted_keywords):
    """
    Updates the bid for each keyword using the keyword ID.
    """
    for keyword in adjusted_keywords:
        try:
            headers = HEADERS
            headers['Content-Type'] = 'application/vnd.spKeyword.v3+json'
            headers['Accept'] = 'application/vnd.spKeyword.v3+json'
            response = requests.put(
                f'{API_URL}/sp/keywords/',
                headers=headers,
                json={"keywords": [{"bid": keyword["suggested_bid"],
                                    "state": keyword["state"], "keywordId": keyword["keywordId"]}]}
            )

            if response.status_code == 200:
                print(
                    f"Bid for keyword {keyword['keywordId']} updated to {new_bid}")
            else:
                print(
                    f"Failed to update bid for keyword {keyword['keywordId']}: {response.json()}")
        except Exception as e:
            print(
                f"Error updating bid for keyword {keyword['keywordId']}: {e}")

        time.sleep(settings.API_DELAY)  # Avoid hitting API rate limits


keywords = []

for index, row in df.iterrows():
    if (row["state"] != "enabled"):
        continue
    keywords.append({
        "keywordId": str(row["keywordId"]),
        "suggested_bid": row["bid"],
        # "state": "disabled"
        "state": str(row["state"]).upper()
    })

print(keywords)

# update_keyword_bids(keywords)

# %%
# new version


def update_keyword_bids(df_with_optimized_bids):
    """
    Updates the bid for each keyword using the optimized bids from the DataFrame.

    Args:
        df_with_optimized_bids: DataFrame containing the optimized bids
                               Must contain columns: keywordId, new_bid, state

    Returns:
        List of dictionaries with update results
    """
    results = []

    # Filter only enabled keywords with new bids
    keywords_to_update = df_with_optimized_bids[
        (df_with_optimized_bids['state'] == 'enabled') &
        (df_with_optimized_bids['new_bid'].notna())
    ]

    print(f"Updating bids for {len(keywords_to_update)} keywords...")

    for index, row in keywords_to_update.iterrows():
        keyword_id = str(row['keywordId'])
        new_bid = float(row['new_bid'])
        state = str(row['state']).upper()

        try:
            headers = HEADERS.copy()
            headers['Content-Type'] = 'application/vnd.spKeyword.v3+json'
            headers['Accept'] = 'application/vnd.spKeyword.v3+json'

            # Prepare the request payload
            payload = {
                "keywords": [
                    {
                        "bid": new_bid,
                        "state": state,
                        "keywordId": keyword_id
                    }
                ]
            }

            # Make the API request
            response = requests.put(
                f'{API_URL}/sp/keywords/',
                headers=headers,
                json=payload
            )

            # Process the response
            if response.status_code == 200:
                print(f"✅ Bid for keyword {keyword_id} updated to {new_bid}€")
                results.append({
                    'keywordId': keyword_id,
                    'success': True,
                    'new_bid': new_bid,
                    'message': f"Bid updated to {new_bid}€"
                })
            else:
                error_message = response.json(
                ) if response.content else f"Status code: {response.status_code}"
                print(
                    f"❌ Failed to update bid for keyword {keyword_id}: {error_message}")
                results.append({
                    'keywordId': keyword_id,
                    'success': False,
                    'new_bid': new_bid,
                    'message': f"API error: {error_message}"
                })
        except Exception as e:
            print(f"❌ Error updating bid for keyword {keyword_id}: {e}")
            results.append({
                'keywordId': keyword_id,
                'success': False,
                'new_bid': new_bid,
                'message': f"Exception: {str(e)}"
            })

        # Avoid hitting API rate limits
        time.sleep(settings.API_DELAY)

    # Summary
    success_count = sum(1 for r in results if r['success'])
    print(f"✅ Successfully updated {success_count} of {len(results)} keywords")

    return results
